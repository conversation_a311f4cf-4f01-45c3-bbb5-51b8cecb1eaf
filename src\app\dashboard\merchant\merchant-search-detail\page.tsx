import MerchantSearchDetail from "@/components/page-ui/merchant/merchant-search-detail";
import TitleHeader from "@/components/ui/title-header";
import { getConfig } from "@/lib/api-service";
import { authOptions } from "@/auth";
import { Session } from "next-auth";
import { getServerSession } from "next-auth/next";
import { Metadata } from "next/types";
import React from 'react';

export const metadata: Metadata = {
    title: "Search Merchant Details",
};

export default async function MerchantSearchDetailPage() {
    const session = await getServerSession(authOptions);
    const configs = await getConfig();
    if (!session) {
        return <div>Please log in to access this page.</div>;
    }
    return (
        <>
            { <MerchantSearchDetail session={session as Session} configs={configs} /> }
        </>
    );
}