"use client";
import {
    <PERSON>,
    CardContent,
    CardDescription,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Card<PERSON>itle,
} from "@/components/shadcn-ui/card";
import { Label } from "@/components/shadcn-ui/label";
import React, { useEffect, useState } from "react";
import { TableRowIdle, TableRowSkeleton } from "./table-row";
import { ScrollArea, ScrollBar } from "@/components/shadcn-ui/scroll-area";
import { Trash } from "lucide-react";
import { Switch } from "@/components/shadcn-ui/switch";
import AlertModal from "../ui/alert-modal";
import axios from "axios";
import { endPointToken } from "@/lib/utils";
import FixedContent from "@/components/ui/fixed-content";
import { AlertBar, AlertProps } from "../ui/alert-bar";
import { ISearchMerchantIdSchema } from "@/lib/types";
import { Button } from "@/components/shadcn-ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuLabel,
    DropdownMenuRadioGroup,
    DropdownMenuRadioItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/shadcn-ui/dropdown-menu";
import { admigLog } from "@/lib/actions";
import { Input } from "../shadcn-ui/input";

type Props = {
    isLoading: boolean;
    data: any;
    endpoint: string;
    onSearch: (data: ISearchMerchantIdSchema) => void;
    merchantId: string;
    session: any;
};
export default function MerchatDetailTable({ isLoading,
    data,
    endpoint,
    onSearch,
    merchantId,
    session,
}: Props) {
    const [isAlertOpen, setIsAlertOpen] = useState(false);
    const [isLoadingModal, setIsLoadingModal] = useState(false);
    const [showAlert, setShowAlert] = useState<AlertProps | null>(null);
    const [isLoadingTable, setIsLoadingTable] = useState(false);
    const [isLoadingFeatureType, setIsLoadingFeatureType] = useState(false);
    const [selectedMenuProfileId, setSelectedMenuProfileId] = useState("");
    const clearAlert = () => setShowAlert(null);

    const handleToggle = (menuProfileId: string) => {
        setSelectedMenuProfileId(menuProfileId);
        setIsAlertOpen(true);
    };

    const handleCloseAlert = () => {
        setIsAlertOpen(false);
    };

    const toggleWeb = async (
        menuProfileId: number,
        merchantId: string,
        visibleWebOrdering: boolean
    ) => {
        setIsLoadingTable(true);

        const params = {
            menuProfileId,
            merchantId,
            visibleWebOrdering,
            language: "",
        };

        let url = `MenuProfile/SetPatchWebOrdering`;
        try {
            const response = await axios.patch(`${endpoint}/api/${url}`, null, {
                params,
                headers: {
                    Accept: "application/json",
                    key: endPointToken(endpoint),
                },
            });
            if (response.status === 200) {
                setIsLoadingTable(false);
                setShowAlert({
                    type: "success",
                    detail: "Data saved",
                    onClose: clearAlert,
                });
                setTimeout(() => {
                    onSearch({
                        endPoint: endpoint,
                        merchantId: merchantId,
                    });
}


