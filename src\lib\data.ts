import {
  ActivitySquare,
  Eraser,
  Home,
  Paintbrush,
  Store,
  ShoppingBag,
  ShoppingCart,
  MonitorCog,
  Upload,
  LucideIcon,
  KeyRound,
  Database,
  DatabaseZap,
} from "lucide-react";
import { Encrypt } from "./encode";

export interface IInfogrammer {
  name: string;
  subName: string;
  logo: string;
}

export interface INavMenu {
  name: string;
  href: string;
  icon: LucideIcon;
  isActive: boolean;
  sub_menu: ISubMenu[];
  tag: string;
}

export interface ISubMenu {
  name: string;
  href: string;
  isActive: boolean;
  tag: string;
}

export const Infogrammer = [
  {
    name: "Infogrammer",
    subName: "Super Admin",
    logo: "/assets/info_icon.jpeg",
  },
];

export const navMenu = [
  {
    name: "Home",
    href: "/dashboard",
    icon: Home,
    isActive: true,
    sub_menu: [],
    tag: "",
  },
  {
    name: "Order",
    href: "#",
    icon: ShoppingCart,
    isActive: true,
    tag: "",
    sub_menu: [
      {
        name: "Check Order",
        href: "/dashboard/order/check-order",
        isActive: true,
        tag: "",
      },
    ],
  },
  {
    name: "Merchant Info",
    href: "#",
    icon: Store,
    isActive: true,
    tag:"",
    sub_menu: [
      {
        name: "Merchant Detail",
        href: "/dashboard/merchant/merchant-search-detail",
        isActive: true,
        tag: "",
      }
    ],
  },
  {
    name: "Menu Profile",
    href: "#",
    icon: ShoppingBag,
    isActive: true,
    tag: "",
    sub_menu: [
      {
        name: "Search by Merchant ID",
        href: "/dashboard/menu-profile/search-merchant-id",
        isActive: true,
        tag: "",
      },
      {
        name: "Search by Menu Profile ID",
        href: "/dashboard/menu-profile/search-menu-profile-id",
        isActive: true,
        tag: "",
      },
    ],
  },
  {
    name: "Database",
    href: "#",
    icon: DatabaseZap,
    isActive: false,
    tag: "Beta",
    sub_menu: [
      {
        name: "Move Merchant",
        href: "/dashboard/database/move-merchant",
        isActive: true,
        tag: "",
      },
    ],
  },
  {
    name: "Clear Cache",
    href: "#",
    icon: Paintbrush,
    isActive: true,
    tag: "",
    sub_menu: [
      {
        name: "Clear by Order Status",
        href: "/dashboard/clear-cache/order-status",
        isActive: true,
        tag: "",
      },
      {
        name: "Clear by Merchant",
        href: "/dashboard/clear-cache/merchant",
        isActive: true,
        tag: "",
      },
      {
        name: "Clear by All",
        href: "/dashboard/clear-cache/all",
        isActive: true,
        tag: "",
      },
    ],
  },
  {
    name: "Clear Menu",
    href: "#",
    icon: Eraser,
    isActive: true,
    tag: "",
    sub_menu: [
      {
        name: "Clear Menu Profile",
        href: "/dashboard/clear-menu/clear-menu-profile",
        isActive: true,
        tag: "",
      },
      {
        name: "Clear All Menu Profile",
        href: "/dashboard/clear-menu/clear-all-menu-profile",
        isActive: true,
        tag: "",
      },
    ],
  },
  {
    name: "POS Config",
    href: "/dashboard/pos-config",
    icon: MonitorCog,
    isActive: true,
    tag: "",
    sub_menu: [],
  },
  {
    name: "Auto Update",
    href: "/dashboard/auto-update",
    icon: Upload,
    isActive: false,
    tag: "Beta",
    sub_menu: [],
  },
  {
    name: "MQTT",
    href: "#",
    icon: ActivitySquare,
    isActive: true,
    tag: "",
    sub_menu: [
      {
        name: "Playground",
        href: "/dashboard/mqtt/playground",
        isActive: true,
        tag: "",
      },
      {
        name: "Monitor",
        href: "/dashboard/mqtt/monitor",
        isActive: true,
        tag: "",
      },
      {
        name: "Check Status",
        href: "/dashboard/mqtt/check-status",
        isActive: true,
        tag: "",
      },
    ],
  },
  {
    name: "Encrypt / Decrypt",
    href: "/dashboard/encrypt-decrypt",
    icon: KeyRound,
    isActive: true,
    tag: "",
    sub_menu: [],
  },
];

export const endPontList = [
  {
    id: 1,
    name: "API_TRUNK (onlineorderapivn6)",
    url: "https://onlineorderapivn6.azurewebsites.net",
    token: process.env.API_KEY_TRUNK,
    apiLoginPath: "api/loginAdmin",
  },
  {
    id: 2,
    name: "API_TRUNK 2 (apiordering)",
    url: "https://apiordering.azurewebsites.net",
    token: process.env.API_KEY_GONE,
    apiLoginPath: "api/loginAdmin",
  },
  {
    id: 3,
    name: "API_TRUNK 3 (onlineorderapifrontvn6)",
    url: "https://onlineorderapifrontvn6.azurewebsites.net",
    token: process.env.API_KEY_OTHER,
    apiLoginPath: "api/loginAdmin",
  },
  {
    id: 4,
    name: "API_KT (onlineorderapis)",
    url: "https://onlineorderapis.azurewebsites.net",
    token: process.env.API_KEY_KT,
    apiLoginPath: "api/adminAuth",
  },
  {
    id: 5,
    name: "API_LUCKY_KOUEN (onlineorderapibuffetlc)",
    url: "https://onlineorderapibuffetlc.azurewebsites.net",
    token: process.env.API_KEY_LUCKY_KOUEN,
    apiLoginPath: "api/loginAdmin",
  },
  {
    id: 6,
    name: "API_TEENOI (onlineorderapibuffetvn6)",
    url: "https://onlineorderapibuffetvn6.azurewebsites.net",
    token: process.env.API_KEY_TEENOI,
    apiLoginPath: "api/loginAdmin",
  },
  {
    id: 7,
    name: "API_TEENOI 2 (apieasyqrordering)",
    url: "https://apieasyqrordering.azurewebsites.net",
    token: process.env.API_KEY_TEENOI_2,
    apiLoginPath: "api/loginAdmin",
  },
  {
    id: 8,
    name: "WEB_ORDERING",
    url: "https://easyorder.asia:2001",
    token: process.env.API_KEY_WEBORDERING,
    apiLoginPath: "api/loginAdmin",
  },
  {
    id: 9,
    name: "INFO:7702 (Server 113)",
    url: "https://infoeasy.cc:7702",
    token: process.env.API_KEY_7702,
    apiLoginPath: "api/loginAdmin",
  },
  {
    id: 10,
    name: "LOCALHOST:4555",
    url: "http://localhost:4555",
    token: process.env.API_KEY_LOCALHOST,
    apiLoginPath: "api/loginAdmin",
  },
  // wait new endpont
];

export const languageList = [
  { id: 1, name: "Thai", locale: "th-TH" },
  { id: 2, name: "English", locale: "en-US" },
];

export const configs = {
  isFixProject: false /* pos config ยังมีปัญหาตอน reset หลัง submit */,
};

export const databaseList = [
  { id: 1, name: "Server 113", value: Encrypt(process.env.DB_CONNECTION_113 as string) },
  { id: 2, name: "Tencent", value: Encrypt(process.env.DB_CONNECTION_TENCENT as string) },
  { id: 3, name: "Azure: easyorder", value: Encrypt(process.env.DB_CONNECTION_EASYORDER as string) },
  { id: 4, name: "Azure: easyordergone", value: Encrypt(process.env.DB_CONNECTION_EASYORDERGONE as string) },
]

export const initialMQTTConnectionUAT = {
  protocol: "wss",
  host: "infoeasy.cc",
  port: "9001",
  clientId: "info_" + Math.random().toString(16).substring(2, 8),
  username: "infogrammer",
  password: "info1234",
};

export const initialMQTTConnectionProduction = {
  protocol: "wss",
  host: "infoeasy.cc",
  port: "9009",
  clientId: "info_" + Math.random().toString(16).substring(2, 8),
  username: "infogrammer",
  password: "info1234",
};
